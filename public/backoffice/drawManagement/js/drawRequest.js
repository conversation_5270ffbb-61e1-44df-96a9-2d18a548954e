/**
 * Common Draw Request Functions
 * Shared functionality between borrower and lender draw request forms
 */
window.DrawRequestUtils = window.DrawRequestUtils || {};
DrawRequestUtils.config = {
    allowUsersDeleteUploads: false,
    allowUserManageDraws: false
};

DrawRequestUtils.calculateAmountFromPercent = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const cost = parseFloat($percentInput.data('cost')) || 0;
    const requestedAmount = (percent / 100) * cost;

    return Math.max(0, requestedAmount);
};

DrawRequestUtils.calculatePercentFromAmount = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;

    const percent = cost > 0 ? (amount / cost) * 100 : 0;
    return Math.max(0, Math.min(100, percent));
};

DrawRequestUtils.validatePercentInput = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const completedPercent = parseFloat($percentInput.data('completed-percent')) || 0;
    const maxPercent = 100 - completedPercent;

    const $validationMsg = $percentInput.closest('td').find('.validation-message');

    if (percent < 0) {
        $validationMsg.text('Percentage cannot be negative').show();
        $percentInput.addClass('is-invalid');
        return false;
    } else if (percent > maxPercent) {
        $validationMsg.text(`Percentage cannot exceed ${maxPercent.toFixed(2)}% (100% - ${completedPercent.toFixed(2)}% completed)`).show();
        $percentInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $percentInput.removeClass('is-invalid');
        return true;
    }
};

DrawRequestUtils.validateAmountInput = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;
    const completedAmount = parseFloat($amountInput.data('completed-amount')) || 0;
    const maxAmount = cost - completedAmount;

    const $validationMsg = $amountInput.closest('td').find('.validation-message');

    if (amount < 0) {
        $validationMsg.text('Amount cannot be negative').show();
        $amountInput.addClass('is-invalid');
        return false;
    } else if (amount > maxAmount) {
        $validationMsg.text(`Amount cannot exceed $${maxAmount.toFixed(2)} (Total Budget - Completed Renovations)`).show();
        $amountInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $amountInput.removeClass('is-invalid');
        return true;
    }
};

DrawRequestUtils.getPercentageColor = function(percentage) {
    const p = Math.max(0, Math.min(100, percentage));

    if (p < 25) {
        return 'bg-danger';
    } else if (p < 50) {
        return 'bg-info';
    } else if (p < 75) {
        return 'bg-primary';
    } else {
        return 'bg-success';
    }
};

DrawRequestUtils.initializePercentageColors = function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).addClass(DrawRequestUtils.getPercentageColor(percentage));
    });
};

DrawRequestUtils.setupInputHandlers = function(additionalValidationCallback) {
    $('.requested-percent').on('input', function() {
        const $percentInput = $(this);
        const lineItemId = $percentInput.data('line-item-id');
        const $amountInput = $(`.requested-amount[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validatePercentInput($percentInput)) {
            const calculatedAmount = DrawRequestUtils.calculateAmountFromPercent($percentInput);
            $amountInput.val(calculatedAmount.toFixed(2));
            DrawRequestUtils.validateAmountInput($amountInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    $('.requested-amount').on('input', function() {
        const $amountInput = $(this);
        const lineItemId = $amountInput.data('line-item-id');
        const $percentInput = $(`.requested-percent[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validateAmountInput($amountInput)) {
            const calculatedPercent = DrawRequestUtils.calculatePercentFromAmount($amountInput);
            $percentInput.val(calculatedPercent.toFixed(2));
            DrawRequestUtils.validatePercentInput($percentInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    $('.requested-percent, .requested-amount').on('blur', function() {
        const $input = $(this);
        if ($input.hasClass('requested-percent')) {
            DrawRequestUtils.validatePercentInput($input);
        } else {
            DrawRequestUtils.validateAmountInput($input);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });
};

DrawRequestUtils.validateAllInputs = function() {
    let isValid = true;

    $('.requested-percent').each(function() {
        if (!DrawRequestUtils.validatePercentInput($(this))) {
            isValid = false;
        }
    });

    $('.requested-amount').each(function() {
        if (!DrawRequestUtils.validateAmountInput($(this))) {
            isValid = false;
        }
    });

    return isValid;
};

DrawRequestUtils.hasNonZeroValues = function() {
    let hasNonZeroValue = false;

    $('.requested-percent, .requested-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        if (value > 0) {
            hasNonZeroValue = true;
            return false;
        }
    });

    return hasNonZeroValue;
};

DrawRequestUtils.markFunded = function() {
    $('span.percentage').each(function() {
        const $percentageElement = $(this);
        const percentage = parseInt($percentageElement.text());

        if (percentage === 100) {
            const $row = $percentageElement.closest('tr.line-item');
            $row.addClass('funded');
            $row.find('.requested-percent').prop('disabled', true);
            $row.find('.requested-amount').prop('disabled', true);
            $row.css('background', 'linear-gradient(to right, #5ac272ff 0%, #fff 100%)');
        }
    });
};

DrawRequestUtils.hasValidationErrors = function() {
    return $('.is-invalid').length > 0;
};

DrawRequestUtils.generateThumbnail = function(doc) {
    const docName = doc.displayDocName || doc.docName || 'Unnamed Document';
    const fileExtension = docName.split('.').pop().toLowerCase();
    const viewUrl = doc.viewUrl;

    // Image file extensions that can show thumbnails
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

    if (imageExtensions.includes(fileExtension)) {
        // Show actual image thumbnail for image files
        return `
            <div class="doc-thumbnail-container">
                <img src="${viewUrl}"
                     alt="Thumbnail"
                     class="doc-thumbnail-image"
                     data-view-url="${viewUrl}"
                     title="Click to view full image">
            </div>
        `;
    }
    return '';
};

$(document).ready(function() {
    DrawRequestUtils.initializePercentageColors();
    DrawRequestUtils.markFunded();

    DrawRequestUtils.initDocumentHandlers();
});

/**
 * Document Management Functions
 */
DrawRequestUtils.initDocumentHandlers = function() {

    $(document).on('click', '.view-docs-btn', function(e) {
        e.preventDefault();
        const lineItemId = $(this).data('line-item-id');

        if (!lineItemId) {
            toastrNotification('Line item ID not found', 'error');
            return;
        }

        DrawRequestUtils.viewLineItemDocuments(lineItemId);
    });

    // Document upload handlers
    $(document).on('click', '.upload-doc-btn', function(e) {
        e.preventDefault();
        const $row = $(this).closest('tr');

        const $fileInput = $row.find('.line-item-file-input');
        $fileInput.trigger('click');
    });

    $(document).on('change', '.line-item-file-input', function(e) {
        const files = e.target.files;
        const lineItemId = $(this).data('line-item-id');

        if (files.length > 0) {
            DrawRequestUtils.uploadLineItemDocuments(lineItemId, files);
        }

        $(this).val('');
    });
};

DrawRequestUtils.uploadLineItemDocuments = function(lineItemId, files) {
    const LMRId = $('#LMRId').val();

    if (!LMRId) {
        toastrNotification('Missing LMR ID for document upload', 'error');
        return;
    }

    Array.from(files).forEach(file => {
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp',
                            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

        if (!allowedTypes.includes(file.type)) {
            toastrNotification('Unsupported file type: ' + file.name, 'error');
            return;
        }

        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            toastrNotification('File too large: ' + file.name + '. Maximum size is 10MB', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('lineItemId', lineItemId);
        formData.append('LMRId', LMRId);
        formData.append('uploaderType', 'borrower');

        toastrNotification('Uploading: ' + file.name, 'info');

        $.ajax({
            url: '/backoffice/api_v2/draw_management/Docs',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    toastrNotification('Document uploaded successfully: ' + file.name, 'success');
                } else {
                    toastrNotification('Upload failed: ' + (response.message || 'Unknown error'), 'error');
                }
            },
            error: function(xhr, _status, _error) {
                let errorMsg = 'Upload failed for: ' + file.name;
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += ' - ' + xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg += ' - ' + xhr.responseJSON.error;
                }
                toastrNotification(errorMsg, 'error');
            }
        });
    });
};

DrawRequestUtils.viewLineItemDocuments = function(lineItemId) {
    $.ajax({
        url: '/backoffice/api_v2/draw_management/Docs',
        type: 'GET',
        data: { lineItemId: lineItemId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                DrawRequestUtils.showDocumentsModal(lineItemId, response.data.documents);
            } else {
                toastrNotification('Failed to load documents: ' + (response.message || 'Unknown error'), 'error');
            }
        },
        error: function(_xhr, _status, error) {
            toastrNotification('Error loading documents: ' + error, 'error');
        }
    });
};

DrawRequestUtils.showDocumentsModal = function(_lineItemId, documents) {
    const $modalTableBody = $('#lineItemDocsModal tbody');
    $modalTableBody.empty();

    if (documents.length === 0) {
        $modalTableBody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted">No documents found for this line item.</td>
            </tr>
        `);
    } else {
        documents.forEach(function(doc) {
            const uploadDate = new Date(doc.uploadedDate).toLocaleDateString();
            const docName = doc.displayDocName || doc.docName || 'Unnamed Document';

            const rowHtml = `
                <tr class="text-center">
                    <td>
                        <a href="#" class="doc-name-link" data-view-url="${doc.viewUrl}" title="Download Document">
                            ${docName}
                        </a>
                        // Add edit name icon
                    </td>
                    <td>
                        ${DrawRequestUtils.generateThumbnail(doc)}
                    </td>
                    <td>${doc.uploaderType}</td>
                    <td>${uploadDate}</td>
                    <td>
                        <a title="Click To Download Document"
                            target="_blank"
                            data-view-url="${doc.viewUrl}"
                            class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass download-doc-btn">
                            <i class="fa fa-download"></i>
                        </a>
                        ${DrawRequestUtils.config.allowUsersDeleteUploads && DrawRequestUtils.config.allowUserManageDraws ? `
                        <a title="Click To Delete Document"
                            data-doc-id="${doc.docID}"
                            class="btn btn-sm btn-danger btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass delete-doc-btn">
                            <i class="flaticon2-trash"></i>
                        </a>
                        ` : ''}
                    </td>
                </tr>
            `;
            $modalTableBody.append(rowHtml);
        });
    }

    $('#lineItemDocsModal').modal('show');

    DrawRequestUtils.setupDocumentModalHandlers();
};

DrawRequestUtils.setupDocumentModalHandlers = function() {
    $('#lineItemDocsModal').off('click.docHandlers');

    $('#lineItemDocsModal').on('click.docHandlers', '.download-doc-btn', function(e) {
        e.preventDefault();
        const viewUrl = $(this).data('view-url');
        if (viewUrl) {
            const link = document.createElement('a');
            link.href = viewUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            toastrNotification('Document download URL not available', 'error');
        }
    });

    $('#lineItemDocsModal').on('click.docHandlers', '.doc-name-link', function(e) {
        e.preventDefault();
        const viewUrl = $(this).data('view-url');
        if (viewUrl) {
            window.open(viewUrl, '_blank');
        } else {
            toastrNotification('Document view URL not available', 'error');
        }
    });

    // Handle thumbnail image clicks
    $('#lineItemDocsModal').on('click.docHandlers', '.doc-thumbnail-image', function(e) {
        e.preventDefault();
        const viewUrl = $(this).data('view-url');
        if (viewUrl) {
            window.open(viewUrl, '_blank');
        } else {
            toastrNotification('Image view URL not available', 'error');
        }
    });


    // Handle delete document button clicks
    $('#lineItemDocsModal').on('click.docHandlers', '.delete-doc-btn', function(e) {
        e.preventDefault();
        const docID = $(this).data('doc-id');
        const $row = $(this).closest('tr');
        const conf = "Are you sure you want to delete this document?";
        if (!docID) {
            toastrNotification('Document ID not found', 'error');
            return;
        }
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: conf,
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: function () {
                    DrawRequestUtils.deleteLineItemDocument(docID, $row);
                },
                cancel: function () {

                },
            },
        });
    });
};

/**
 * Delete a line item document
 * @param {number} docID - Document ID to delete
 * @param {jQuery} $row - Table row element to remove on success
 */
DrawRequestUtils.deleteLineItemDocument = function(docID, $row) {
    $.ajax({
        url: '/backoffice/api_v2/draw_management/Docs?docID=' + docID,
        type: 'DELETE',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                toastrNotification('Document deleted successfully', 'success');
                $row.fadeOut(300, function() {
                    $row.remove();

                    const $modalTableBody = $('#lineItemDocsModal tbody');
                    if ($modalTableBody.find('tr').length === 0) {
                        $modalTableBody.append(`
                            <tr>
                                <td colspan="5" class="text-center text-muted">No documents found for this line item.</td>
                            </tr>
                        `);
                    }
                });
            } else {
                toastrNotification('Failed to delete document: ' + (response.message || 'Unknown error'), 'error');
            }
        },
        error: function(xhr, _status, error) {
            let errorMsg = 'Error deleting document';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg += ': ' + xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg += ': ' + xhr.responseJSON.error;
            } else {
                errorMsg += ': ' + error;
            }
            toastrNotification(errorMsg, 'error');
        }
    });
};
